const stripeOnramp = StripeOnramp(
    "pk_test_51RWEaa2ZCBhh7ZYzp9XFnx1AkjWl0NOSBOTgCuiqwJuYi4jWZhPVXbRGmYQVgsnbLPWCcCB8FaryigX1ZFtKNIkV00uGc5E0vO"
);

let session;

initialize();

async function initialize() {
    // Fetches an onramp session and captures the client secret
    const response = await fetch("/create-onramp-session", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
            transaction_details: {
                destination_currency: "usdc",
                destination_exchange_amount: "13.37",
                destination_network: "ethereum",
            },
        }),
    });
    const clientSecret = "cos_1ReVSqFBmFhVsSrCp1fDBKui_secret_vokORrZLTSFwKflOfSp1G7OpU00KHnRPkoG";

    session = stripeOnramp
        .createSession({
            clientSecret,
            appearance: {
                theme: "dark",
            },
        })
        .addEventListener("onramp_session_updated", (e) => {
            showMessage(
                `OnrampSession is now in ${e.payload.session.status} state.`
            );
        })
        .mount("#onramp-element");
}

// ------- UI helpers -------

function showMessage(messageText) {
    const messageContainer = document.querySelector("#onramp-message");

    messageContainer.textContent = messageText;
}
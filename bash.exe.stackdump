Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB6A0, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 0007FFFFB6A0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD81B90000 ntdll.dll
7FFD81280000 KERNEL32.DLL
7FFD7ECD0000 KERNELBASE.dll
7FFD80C50000 USER32.dll
7FFD7F560000 win32u.dll
7FFD7FA40000 GDI32.dll
7FFD7F440000 gdi32full.dll
7FFD7F650000 msvcp_win.dll
7FFD7F160000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD81A70000 advapi32.dll
7FFD81760000 msvcrt.dll
7FFD7FB30000 sechost.dll
7FFD7F0B0000 bcrypt.dll
7FFD7F8C0000 RPCRT4.dll
7FFD7E470000 CRYPTBASE.DLL
7FFD7F0E0000 bcryptPrimitives.dll
7FFD7FAF0000 IMM32.DLL

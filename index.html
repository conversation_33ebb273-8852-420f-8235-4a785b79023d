<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Smile ID Verification</title>
    <script src="https://cdn.smileidentity.com/js/v1.4.2/smart-camera-web.js"></script>
    <style>
      body,
      html {
        margin: 0;
        padding: 0;
        height: 100%;
        width: 100%;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Helvetica, Arial, sans-serif;
        background-color: #ffffff;
      }
      .container {
        width: 100%;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ffffff;
      }
      smart-camera-web {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Smart Camera Web components will be added dynamically -->
    </div>

    <script>
      // Get URL parameters to configure the component
      const urlParams = new URLSearchParams(window.location.search);
      const verificationType = urlParams.get("type") || "selfie";
      const countryCode = urlParams.get("country") || "NG";
      const documentType = urlParams.get("docType") || "passport";

      // Function to send messages to React Native
      function sendToReactNative(type, data) {
        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify({ type, data }));
        } else {
          console.log("ReactNativeWebView not available", { type, data });
        }
      }

      // Create the appropriate component based on verificationType
      const container = document.querySelector(".container");
      if (verificationType === "selfie") {
        // Create selfie component
        const selfieComponent = document.createElement("smart-camera-web");
        selfieComponent.setAttribute("mode", "selfie-only");
        selfieComponent.setAttribute("allow-agent-mode", "no");
        container.appendChild(selfieComponent);

        // Add event listener for selfie capture
        selfieComponent.addEventListener("imagesComputed", (e) => {
          console.log("Selfie capture completed:", e.detail);
          sendToReactNative("captureComplete", {
            type: "selfie",
            data: e.detail,
          });
        });

        // Add event listener for errors
        selfieComponent.addEventListener("smart-camera-web.error", (e) => {
          console.error("Selfie capture error:", e.detail);
          sendToReactNative("error", {
            type: "selfie",
            message: e.detail.message || "Unknown error",
            details: e.detail,
          });
        });
      } else if (verificationType === "document") {
        // Create document component
        const documentComponent = document.createElement("smart-camera-web");
        documentComponent.setAttribute("capture-id", "");
        documentComponent.setAttribute("allow-agent-mode", "no");
        documentComponent.setAttribute("hide-back-of-id", "no");
        documentComponent.setAttribute("document-capture-modes", "camera");
        container.appendChild(documentComponent);

        // Add event listener for document capture
        documentComponent.addEventListener("imagesComputed", (e) => {
          console.log("Document capture completed:", e.detail);
          sendToReactNative("captureComplete", {
            type: "document",
            data: e.detail,
          });
        });
      } else {
        // Invalid verification type
        console.error("Invalid verification type:", verificationType);
        sendToReactNative("error", {
          message: "Invalid verification type: " + verificationType,
        });
      }
      // Let React Native know the page is loaded
      window.addEventListener("load", () => {
        console.log("Page loaded with verification type:", verificationType);
        sendToReactNative("loaded", {
          verificationType: verificationType,
          documentType: documentType,
          countryCode: countryCode,
        });
      });
      // For testing in browser
      if (!window.ReactNativeWebView) {
        window.ReactNativeWebView = {
          postMessage: function (message) {
            console.log("Message to React Native (test):", message);
          },
        };
      }
    </script>
  </body>
</html>
